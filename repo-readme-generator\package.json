{"name": "repo-readme-generator", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.1.1", "@vercel/analytics": "^1.3.1", "clsx": "^2.1.1", "framer-motion": "^11.2.12", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.2.1", "react-markdown": "^9.0.1", "tailwind-merge": "^2.3.0"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "vite": "^5.3.1"}}